package com.optimaitalia.controller;

import com.optimaitalia.service.AttachmentService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController()
@RequestMapping("/api/attachment")
public class AttachmentController {

    private static final Logger logger = LogManager.getLogger(AttachmentController.class);

    private final AttachmentService attachmentService;

    public AttachmentController(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    @PostMapping(consumes = {"multipart/form-data"})
    public ResponseEntity sendAttachment(@RequestPart("incidentId") String incidentId,
                                         @RequestPart("files") MultipartFile[] files,
                                         @RequestPart("message") String message) {
        return attachmentService.sendIncidentWithMultiAttachment(files, incidentId, message);
    }
}
