package com.optimaitalia.controller;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.exception.PaymentException;
import com.optimaitalia.model.wrappers.mobile.MobilePaymentRequest;
import com.optimaitalia.model.wrappers.payment.*;
import com.optimaitalia.service.PaymentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/api")
public class PaymentController {

    private final PaymentService paymentService;

    public PaymentController(PaymentService paymentService) {
        this.paymentService = paymentService;
    }

    @GetMapping("/properties/payment")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ModelAndView doPayment(@RequestParam List<Long> invoices, @RequestParam Long clientId) throws Exception, PaymentException {
        if (clientId == null) {
            throw new PaymentException("Not Authorized");
        }
        return paymentService.doPayment(invoices, clientId);
    }

    @GetMapping("/properties/payment/mobile")
    @PreAuthorize("#request.clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.clientId, authentication.principal.uid)")
    public @ResponseBody String rechargeSimCard(@ModelAttribute MobilePaymentRequest request) throws PaymentException, ValidateException {
        return paymentService.rechargeSimCard(request);
    }

    @GetMapping("/properties/payment/rate")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ModelAndView doPayRate(@RequestParam List<String> listRate, @RequestParam Long clientId) throws Exception, PaymentException {
        if (clientId == null) {
            throw new PaymentException("Not Authorized");
        }
        return paymentService.doPayRate(listRate, clientId);
    }


    @PostMapping("/properties/payment/pagamenti")
    @PreAuthorize("#request.getCodiceCliente()!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.getCodiceCliente(), authentication.principal.uid)")
    public @ResponseBody PagamentiPaymentResponse doPagamentiCartaCreditoPayment(@RequestBody PagamentiPaymentRequest request) {
        return paymentService.getPaymentModalitaPagamentiUrl(request);
    }

    @PostMapping("/properties/payment/nexi")
    @PreAuthorize("#request.getCodiceCliente()!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.getCodiceCliente(), authentication.principal.uid)")
    public @ResponseBody NexiPaymentResponse getNexiPaymentInfo(@RequestBody NexiPaymentRequest request) {
        return paymentService.getNexiPaymentInfo(request);
    }

    @PostMapping("/properties/payment/autoricarica/activate")
    @PreAuthorize("#request.getCodiceCliente()!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.getCodiceCliente(), authentication.principal.uid)")
    public @ResponseBody PagamentiPaymentResponse doPagamentiAutoricaricaPayment(@RequestBody PagamentiPaymentRequest request) {
        return paymentService.getAutoricaricaURL(request);
    }

    @PostMapping("/properties/payment/autoricarica/paypal/activate")
    public @ResponseBody PayPalResponse doPayPalAutoricaricaPayment(@RequestBody ActivatePayPalAutoricaricaRequest request) {
        return paymentService.getAutoricaricaPayPalURL(request);
    }

    @PostMapping("/properties/payment/autoricarica/information")
    @PreAuthorize("#request.getCOD_CLIENTE()!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.getCOD_CLIENTE(), authentication.principal.uid)")
    public @ResponseBody CheckSubscriptionAutoricaricaResponse getInformationAboutAutoricarica(@RequestBody CheckSubscriptionAutoricaricaRequest request) {
        return paymentService.getInformationAboutAutoricarica(request);
    }

    @PostMapping("/properties/payment/autoricarica/deactivate")
    public @ResponseBody Map deactivateAutoricarica(@RequestBody DeactivateAutoRicaricaRequest request) {
        return paymentService.deactivateAutoricarica(request);
    }
}
