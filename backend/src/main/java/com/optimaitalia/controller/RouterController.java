package com.optimaitalia.controller;

import com.optimaitalia.model.routerInfo.RouterInfo;
import com.optimaitalia.service.PDFService;
import com.optimaitalia.service.RouterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/api")
public class RouterController {
    private final PDFService pdfService;
    private final RouterService routerService;

    @Autowired
    public RouterController(PDFService pdfService, RouterService routerService) {
        this.pdfService = pdfService;
        this.routerService = routerService;
    }


    @GetMapping(path = "/routerInfo")
    public RouterInfo routerInfo(@RequestHeader("clientid") Long clientId) {
        return routerService.getRouterInfo(clientId);
    }
    @GetMapping(path = "/routerAggiornaPdf")
    public ResponseEntity getPdf(@RequestHeader("fileName") String name) {
        return pdfService.getFileFromResource(name);
    }
}
