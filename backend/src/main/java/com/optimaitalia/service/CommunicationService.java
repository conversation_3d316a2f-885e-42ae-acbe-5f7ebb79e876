package com.optimaitalia.service;

import com.optimaitalia.model.wrappers.communication.CommunicationNote;
import com.optimaitalia.model.wrappers.communication.CustomerEmailInfo;
import com.optimaitalia.model.wrappers.communication.RecommendedBlock;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

public interface CommunicationService {

    List<CustomerEmailInfo> getCustomerCommunicationEmailInfo(String clientId);

    List<RecommendedBlock> loadCustomerRecommendationBlocks(@PathVariable String clientId);

    ResponseEntity<byte[]> downloadRecommendedFile(String clientId, String fileName);

    ResponseEntity<byte[]> downloadCommuniationNoteFile(String clientId, String fileName);

    List<CommunicationNote> getCommunicationNote(String clientId);

}
