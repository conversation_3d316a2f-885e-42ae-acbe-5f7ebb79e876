package com.optimaitalia.service;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.condominio.Condominio;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.model.wrappers.user.requests.ChangeAddressRequest;
import com.optimaitalia.model.wrappers.user.requests.ChangePasswordIdentificationRequest;
import com.optimaitalia.model.wrappers.user.requests.Geolocation;
import com.optimaitalia.model.wrappers.user.requests.PersonalDataChangeRequest;
import com.optimaitalia.model.wrappers.user.response.ChangePersonalDataResponse;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface UserDataService {

    ChangePersonalDataResponse changePersonalUserData(PersonalDataChangeRequest userDataChangeRequest) throws ValidateException;

    ChangePersonalDataResponse changePasswordIdentification(ChangePasswordIdentificationRequest userDataChangeRequest);

    IncidentEventResponse changeBillingAddress(ChangeAddressRequest request) throws ValidateException;

    List<Condominio> getUserCondominioInfo(String clientId);

    ResponseEntity<?> getUserCondominioFiscalCode(String clientId);

    Geolocation getGeolocationInfo();
}
