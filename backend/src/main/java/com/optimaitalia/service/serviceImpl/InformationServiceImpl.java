package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.model.userData.PaymentData;
import com.optima.security.model.userData.UserData;
import com.optima.security.service.AuthenticationService;
import com.optima.security.service.SecurityService;
import com.optima.security.service.impl.LoginServiceImpl;
import com.optimaitalia.model.Chart;
import com.optimaitalia.model.Contracts;
import com.optimaitalia.model.Shipment;
import com.optimaitalia.model.ShipmentHeader;
import com.optimaitalia.model.queryParamsToJSON.Criteria;
import com.optimaitalia.model.wrappers.contabile.Contabile;
import com.optimaitalia.model.wrappers.dilazione.Rate;
import com.optimaitalia.model.wrappers.invoice.Invoice;
import com.optimaitalia.model.wrappers.offer.Offer;
import com.optimaitalia.model.wrappers.publicAdministrator.BillingCenterInformation;
import com.optimaitalia.model.wrappers.user.response.GetLatestCreditPolicyStatusResponse;
import com.optimaitalia.model.wrappers.user.response.UserClusterInfo;
import com.optimaitalia.service.InformationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.*;



@Service
public class InformationServiceImpl implements InformationService {

    private static final Logger logger = LogManager.getLogger(InformationServiceImpl.class);

    private @Value("${restdata.urls.chart}")
    String chartUrl;

    private @Value("${restdata.urls.userdata}")
    String userDataUrl;

    private @Value("${restdata.urls.grantModalitaPagamento}")
    String creditCardStateUrl;

    private @Value("${restdata.urls.contracts}")
    String contractDataUrl;

    private @Value("${restdata.urls.invoices}")
    String invoicesUrl;

    private @Value("${restdata.urls.billing-center}")
    String billingCenterUrl;

    private @Value("${restdata.urls.dilazioneCliente}")
    String dilazioneUrl;

    private @Value("${restdata.urls.services}")
    String servicesUrl;

    private @Value("${restdata.urls.spedizionetestata}")
    String shipmentUrl;

    private @Value("${restdata.urls.spedizione}")
    String shipmentUrlData;

    private @Value("${restdata.urls.userauthenticate}")
    String authUrl;

    private @Value("${restdata.urls.modalitaPagamento}")
    String modalitaUrl;

    private @Value("${restdata.urls.account}")
    String accounting;

    private @Value("${restdata.urls.segnalazione}")
    String signalUrl;

    @Value("${restdata.urls.offers-data}")
    private String offersDataUrl;

    @Value("${restdata.urls.customer-cluster-info-url}")
    private String userClusterInfoUrl;

    @Value("${restdata.urls.сredit-policy-status}")
    private String userCreditPolicyStatus;

    private final RestTemplate restTemplate;

    private final SecurityService securityService;

    private final ObjectMapper objectMapper;

    private final AuthenticationService authenticationService;
    private final LoginServiceImpl loginService;

    @Autowired
    public InformationServiceImpl(RestTemplate restTemplate, SecurityService securityService, ObjectMapper objectMapper, AuthenticationService authenticationService, LoginServiceImpl loginService) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
        this.loginService = loginService;
    }

    @Override
    public List<Chart> getChartInfo(String clientId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<Map> exchange = restTemplate.exchange(chartUrl, HttpMethod.GET, httpEntity, Map.class, clientId);
        return objectMapper.convertValue(exchange.getBody().get("List"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, Chart.class));
    }


    @Override
    @SuppressWarnings("unchecked")
    public UserData getUserData(String clientId) {
        logger.info("Obtaining chart info for user with id {}", clientId);
        UserData finalUserData = authenticationService.getUserData(clientId, userDataUrl);
        PaymentData paymentData = authenticationService.getUserPaymentData(clientId, modalitaUrl);
        finalUserData.setPaymentData(paymentData);
        transform(paymentData);
        logger.info("User's chart info is obtained");
        return finalUserData;
    }


    @Override
    @SuppressWarnings("unchecked")
    public Contabile getContabileData(String clientId) {
        logger.info("Obtaining account data for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<Map> exchange = restTemplate.exchange(accounting, HttpMethod.GET, httpEntity, Map.class, clientId);
        logger.info("Account data has been obtained.");
        Map<String, Object> response = (Map<String, Object>) exchange.getBody();
        return objectMapper.convertValue(response, Contabile.class);
    }

    @Override
    public List<Offer> getOffersData(Long clientId) {
        logger.info("Obtaining list of offers for client with id {}", clientId);
        if (clientId != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            Map<String, Object> body = new HashMap<>();
            body.put("Cliente", clientId);
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            ResponseEntity<Map> exchange = restTemplate.exchange(offersDataUrl, HttpMethod.POST, httpEntity, Map.class);
            logger.info("List of offers has been obtained.");
            return objectMapper.convertValue(exchange.getBody().get("listaOfferte"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Offer.class));
        }
        return new ArrayList<>();
    }

    @Override
    public List<UserClusterInfo> findUserClusterInfo(String clientId) {
        if (!StringUtils.isEmpty(clientId)) {
            Map<String, Object> body = new HashMap<>();
            body.put("@idCliente", clientId);
            Map response = restTemplate.exchange(userClusterInfoUrl, HttpMethod.POST, new HttpEntity<>(body), Map.class).getBody();
            if (response != null) {
                return objectMapper.convertValue(response.get("response"), objectMapper.getTypeFactory()
                        .constructCollectionType(List.class, UserClusterInfo.class));
            }
        }
        return Collections.emptyList();
    }

    private void transform(PaymentData userData) {
        Map<String, String> map = new HashMap<>();
        map.put("SDD", "Addebito diretto");
        map.put("BB", "Bonifico bancario");
        map.put("BP", "Bollettino postale");
        map.put("Bollettino Postale", "Bollettino postale");
        String modalitaDiPagamento = userData.getModalita();
        map.forEach((String k, String v) -> {
            if (modalitaDiPagamento != null && modalitaDiPagamento.contains(k)) {
                userData.setModalita(v);
            }
        });
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Contracts> getContractsData(String clientId) {
        logger.info("Obtaining list of contracts for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

        Map<String, Object> body = new HashMap<>();
        body.put("idCliente", clientId);
        body.put("cancellato", "false");
        body.put("importato", "true");
        body.put("idContratto", null);
        body.put("ragioneSociale", null);
        body.put("codiceFiscale", null);
        body.put("telefono", null);
        body.put("checkCard", null);
        body.put("partitaIVA", null);

        HttpEntity<?> request = new HttpEntity<>(body, headers);
        Map response = restTemplate.postForObject(contractDataUrl, request, Map.class);
        logger.info("List of contracts has been obtained.");
        if (response == null || response.get("response") == null) return new ArrayList<>();
        Map<String, Object> responseData = (Map<String, Object>) response.get("response");

        return objectMapper.convertValue(responseData.get("contratti"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, Contracts.class));
    }

    @Override
    public List<Invoice> getInvoicesData(String clientId) {
        Map response = getResponseFromInvoicesUrl(clientId);
        logger.info("List of invoices has been obtained.");
        return objectMapper.convertValue(response.get("InvoiceList"), objectMapper.getTypeFactory().constructCollectionType(List.class, Invoice.class));
    }

    @Override
    public BillingCenterInformation getBillingCenterInformation(String clientId) {
        logger.info("Obtaining billing center information for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("@idCliente", clientId);
        HttpEntity<?> httpEntity = new HttpEntity<>(body, headers);
        return restTemplate.exchange(this.billingCenterUrl, HttpMethod.POST, httpEntity, BillingCenterInformation.class).getBody();
    }

    @Override
    public List<Rate> getRatesData(String clientId) {
        logger.info("Obtaining list of rates for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Criteria criteria = new Criteria(Long.parseLong(clientId));
        HttpEntity<?> request = new HttpEntity<>(criteria, headers);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.dilazioneUrl, HttpMethod.GET, request,
                Map.class, clientId);
        Map<String, Object> response = (Map<String, Object>) exchange.getBody();
        Map<String, Object> dilazioneMap = (Map<String, Object>) response.get("dilazione");
        logger.info("List of rates has been obtained.");
        return objectMapper.convertValue(dilazioneMap.get("rate"), objectMapper.getTypeFactory().constructCollectionType(List.class, Rate.class));

    }

    private ClientHttpRequestFactory getClientHttpRequestFactory() {
        int timeout = 30000;
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory
                = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setReadTimeout(timeout);
        clientHttpRequestFactory.setConnectionRequestTimeout(timeout);
        clientHttpRequestFactory.setConnectTimeout(timeout);
        return clientHttpRequestFactory;
    }


    public Object getSaldo(String clientId) {
        Map response = getResponseFromInvoicesUrl(clientId);
        logger.info("List of invoices has been obtained.");
        if (response != null) {
            return response.get("Saldo");
        }
        return null;
    }

    @Override
    public Object getSaldoInScadenza(String clientId) {
        Map response = getResponseFromInvoicesUrl(clientId);
        logger.info("List of invoices has been obtained.");
        if (response != null) {
            return response.get("SaldoInScadenza");
        }
        return null;
    }

    private Map getResponseFromInvoicesUrl(String clientId) {
        logger.info("Obtaining list of invoices for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Criteria criteria = new Criteria(Long.parseLong(clientId));
        HttpEntity<?> request = new HttpEntity<>(criteria, headers);
        return restTemplate.postForObject(invoicesUrl, request, Map.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<com.optimaitalia.model.services.Service> getServices(String clientId) {
        logger.info("Obtaining services for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<Map> exchange;
        RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
        try {
            exchange = restTemplate.exchange(servicesUrl, HttpMethod.GET, httpEntity, Map.class, clientId);
            logger.info("Services has been obtained.");
        } catch (HttpServerErrorException ex) {
            logger.error("Error while trying to obtain services fo user with id {}. Error: {}", clientId, ex);
            return new ArrayList<>();
        }
        if (exchange.getBody().get("response") == null) return new ArrayList<>();

        Map<String, Object> responseMap = (Map<String, Object>) exchange.getBody().get("response");
        return objectMapper.convertValue(responseMap.get("servizio"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, com.optimaitalia.model.services.Service.class));
    }

    @Override
    public List<Shipment> getShipment(String clientId) {
        logger.info("Obtaining list of shipments for user with id {}", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

        Map<String, Object> body = new HashMap<>();
        body.put("listIdsoggettoesterno", new Object[]{clientId});
        body.put("limit", 20);
        body.put("offset", 0);

        HttpEntity<?> request = new HttpEntity<>(body, headers);
        Map response = restTemplate.postForObject(shipmentUrl, request, Map.class);
        logger.info("List of shipments has been updated.");
        List<Shipment> shipment = objectMapper.convertValue(response.get("listResponse"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, Shipment.class));

        Map<String, Object> shipmentHeaderRequestBody = new HashMap<>();
        Object[] idSpedisioniArray = new Object[1];
        logger.info("Obtaining details for each shipment.");
        shipment.forEach((Shipment i) -> {
            headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            idSpedisioniArray[0] = i.getId();
            shipmentHeaderRequestBody.put("idSpedisioniList", idSpedisioniArray);
            HttpEntity<?> shipmentParameter = new HttpEntity<>(shipmentHeaderRequestBody, headers);
            Map shipmentsData = restTemplate.postForObject(shipmentUrlData, shipmentParameter, Map.class);
            List<ShipmentHeader> shipmenthesder = objectMapper.convertValue(shipmentsData.get("listSpedizioniDettaglio"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ShipmentHeader.class));
            i.setShipmentHeader(shipmenthesder.get(0));
        });
        logger.info("Shipment details have been obtained.");
        return shipment;
    }

    @Override
    public Boolean checkIfCreditCardEnabled(String clientId) {
        if (!StringUtils.isEmpty(clientId)) {
            Map<String, String> body = new HashMap<>();
            body.put("idCliente", clientId);
            body.put("processo", "CartaCredito");
            body.put("target", "Abilitati");
            HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(body);
            ResponseEntity<Map> exchange = restTemplate.exchange(creditCardStateUrl, HttpMethod.POST, httpEntity, Map.class);
            if (exchange.getBody() != null) {
                return !"KO".equals(exchange.getBody().get("response"));
            }
        }
        return false;
    }

    @Override
    public String getUserCodeFromIncidentEvent(String clientId){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("offset", 0);
        body.put("order", "CreatedOn");
        body.put("direction", "desc");
        HttpEntity<Map> httpEntity = new HttpEntity<>(body, headers);
        Map response = restTemplate.exchange(signalUrl, HttpMethod.POST, httpEntity, Map.class, clientId).getBody();
        Map<String, Object> responseData = (Map<String, Object>) response.get("response");
        List<Object> contents = (List<Object>) responseData.get("Content");
        return contents.get(0).toString().substring(61,97);
    }

    @Override
    public GetLatestCreditPolicyStatusResponse getLatestCreditPolicyStatus(String clientId) {
        return restTemplate.exchange(userCreditPolicyStatus, HttpMethod.GET, null, GetLatestCreditPolicyStatusResponse.class, clientId).getBody();
    }
}
