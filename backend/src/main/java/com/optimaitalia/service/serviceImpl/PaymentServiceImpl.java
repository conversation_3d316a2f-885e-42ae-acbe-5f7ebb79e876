package com.optimaitalia.service.serviceImpl;

import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optimaitalia.exception.PaymentException;
import com.optimaitalia.model.wrappers.dilazione.Rate;
import com.optimaitalia.model.wrappers.invoice.Invoice;
import com.optimaitalia.model.wrappers.mobile.MobilePaymentRequest;
import com.optimaitalia.model.wrappers.mobile.conracts.ContractRecord;
import com.optimaitalia.model.wrappers.payment.*;
import com.optimaitalia.service.InformationService;
import com.optimaitalia.service.MobileService;
import com.optimaitalia.service.PaymentService;
import com.optimaitalia.utils.CryptoUtils;
import com.optimaitalia.utils.OptimaUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
public class PaymentServiceImpl implements PaymentService {

    private static final Logger logger = LogManager.getLogger(PaymentServiceImpl.class);

    @Value("${payment.secret.key}")
    private String aesEncryptKey;

    @Value("${payment.invoices.url}")
    private String paymentUrl;

    @Value("${payment.rate.url}")
    private String paymentRateUrl;

    @Value("${payment.mobile.url}")
    private String mobilePaymentUrl;

    @Value("${payment.success.url}")
    private String paymentRedirectUrl;

    @Value("${paymentRate.success.url}")
    private String paymentRateRedirectUrl;

    @Value("${payment.filed.url}")
    private String paymentFailedUrl;

    @Value("${payment.mobile.success.url}")
    private String mobilePaymentRedirectUrl;

    @Value("${payment.mobile.filed.url}")
    private String mobilePaymentFailedRedirectUrl;

    @Value("${payment.mobile.channel.id}")
    private String mobilePaymentChannelId;

    @Value("${payment.modalita.pagamenti.url}")
    private String paymentCartaCreditoUrl;

    @Value("${payment.nexi.url}")
    private String paymentNexiUrl;

    @Value("${payment.modalita.pagamenti.credentials}")
    private String paymentCreditCardCredentials;

    @Value("${payment.failed.redirect.url}")
    private String paymentFailedRedirectUrl;

    @Value("${payment.ok.url}")
    private String paymentOkUrl;

    @Value("${payment.ko.url}")
    private String paymentKoUrl;

    @Value("${payment.callback.url}")
    private String paymentcallbackUrl;

    @Value("${payment.autoricarica.ok.url}")
    private String paymentAutoricaricaOkUrl;

    @Value("${payment.autoricarica.callback.url}")
    private String paymentAutoricaricaCallbackUrl;

    @Value("${payment.autoricarica.paypal.callback.url}")
    private String paymentAutoricaricaPaypalCallbackUrl;


    @Value("${payment.autoricarica.check.subscription.url}")
    private String paymentAutoricaricaCheckSubscription;

    @Value("${payment.autoricarica.paypal.url}")
    private String paymentAutoricaricaPayPalURL;

    @Value("${payment.deactivate.autoricarica.subscription.url}")
    private String paymentDeactivateAutoricarica;

    private final InformationService informationService;

    private final RestTemplate restTemplate;

    private final RestTemplate sslRestTemplate;

    private final MobileService mobileService;

    private final OvalValidator ovalValidator;

    public PaymentServiceImpl(InformationService informationService, RestTemplate restTemplate,
                              RestTemplate sslRestTemplate, MobileService mobileService, OvalValidator ovalValidator) {
        this.informationService = informationService;
        this.restTemplate = restTemplate;
        this.sslRestTemplate = sslRestTemplate;
        this.mobileService = mobileService;
        this.ovalValidator = ovalValidator;
    }

    @Override
    public ModelAndView doPayment(List<Long> invoices, Long clientId) throws Exception, PaymentException {
        logger.info("Initializing invoice payment for user with id {}.", clientId);
        ModelAndView mv = new ModelAndView();

        if (invoices == null || invoices.isEmpty() || clientId == null) {
            logger.info("Initialization has been stopped. Obtained invalid parameters.");
            mv.setView(new RedirectView(paymentFailedUrl));
            return mv;
        }
        logger.info("Obtaining invoice details from remote service.");
        Optional<List<Invoice>> invoicesData = Optional.ofNullable(informationService.getInvoicesData(clientId.toString()))
                .map(list -> list.stream().filter((s) ->
                        invoices.contains(s.getId())).collect(Collectors.toList()));

        if (!invoicesData.isPresent() || invoices.size() != invoicesData.get().size()) {
            logger.info("Initialization has been stopped. Obtained invalid payment parameters.");
            mv.setView(new RedirectView(paymentFailedUrl));
            return mv;
        }
        logger.info("Invoices have been obtained. Obtaining payment url.");
        Payment payment = new Payment();
        payment.setClientId(clientId);

        for (Invoice invoice : invoicesData.get()) {
            this.separateIfIsNotEmpty(payment.getInvoiceNumberStr());
            payment.getInvoiceNumberStr().append(invoice.getNumeroFattura()).append("/").append(invoice.getInvoiceSeries());
            this.separateIfIsNotEmpty(payment.getInvoiceAmountStr());
            BigDecimal amount = invoice.getTotal().add(invoice.getTotalEvasion().negate());
            String amountStr = this.buildPaymentAmount(amount);
            this.separateIfIsNotEmpty(payment.getInvoiceDateStr());
            payment.getInvoiceDateStr().append(OptimaUtils.getDateAsString(invoice.getStartDate()));
            this.separateIfIsNotEmpty(payment.getInvoiceExternalIdsStr());
            payment.getInvoiceAmountStr().append(amountStr);
            payment.getInvoiceExternalIdsStr().append(invoice.getId());
        }

       /* String amountStr = this.buildPaymentAmount(amount);
        payment.getInvoiceAmountStr().append(amountStr);
        for (int i = 1; i < invoicesData.get().size(); i++) {
            this.separateIfIsNotEmpty(payment.getInvoiceAmountStr());
            payment.getInvoiceAmountStr().append(this.buildPaymentAmount(BigDecimal.ZERO));
        }*/

        String paymentUrl = this.getPaymentUrl(payment);
        logger.info("Payment url has been obtained...");
        mv.setView(new RedirectView(paymentUrl));
        return mv;
    }

    @Override
    public String rechargeSimCard(MobilePaymentRequest request) throws PaymentException, ValidateException {
        ovalValidator.validate(request);
        logger.info("Initializing recharging for sim card {}, user with id {}", request.getSimNumber(), request.getClientId());
        List<ContractRecord> mobileContracts = mobileService.findMobileContracts(null, request.getSimNumber());
        ContractRecord contractRecord;
        if (!mobileContracts.isEmpty() && (contractRecord = mobileContracts.get(0)) != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("Partnerstyret_id_MOBILE", String.valueOf(request.getClientId()));
            body.add("CustomerID_MOBILE", String.valueOf(request.getClientId()));
            body.add("PhoneNumber_MOBILE", request.getSimNumber().toString());
            body.add("Amount_MOBILE", this.buildPaymentAmount(request.getAmount()));
            body.add("subscriptionId_MOBILE", contractRecord.getId().toString());
            body.add("channelId_MOBILE", mobilePaymentChannelId);
            body.add("URL_OK_MOBILE", mobilePaymentRedirectUrl);
            body.add("URL_KO_MOBILE", mobilePaymentFailedRedirectUrl + request.getSimNumber());
            HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
            ResponseEntity<String> exchange = restTemplate.exchange(this.mobilePaymentUrl, HttpMethod.POST, entity, String.class);
            String location = this.getPaymentRedirectLocationUrl(exchange.getHeaders().get("Location"));
            logger.info("Payment link has been obtained.");
            return location;
        }
        logger.info("Initialization has been stopped. No contract record has been found for sim number {}", request.getSimNumber());
        throw new PaymentException("Il numero selezionato non è un numero appartenente alla rete Optima");
    }

    @Override
    public ModelAndView doPayRate(List<String> rateList, Long clientId) throws Exception, PaymentException {
        logger.info("Initializing rate payment for user with id {}.", clientId);
        ModelAndView mv = new ModelAndView();

        if (rateList == null || rateList.isEmpty() || clientId == null) {
            logger.info("Initialization has been stopped. Obtained invalid parameters.");
            mv.setView(new RedirectView(paymentFailedUrl));
            return mv;
        }
        logger.info("Obtaining rates details from remote service.");
        Optional<List<Rate>> ratesData = Optional.ofNullable(informationService.getRatesData(clientId.toString()))
                .map(list -> list.stream().filter((s) ->
                        rateList.contains(s.getIdRata())).collect(Collectors.toList()));

        if (!ratesData.isPresent() || rateList.size() != ratesData.get().size()) {
            logger.info("Initialization has been stopped. Obtained invalid payment parameters.");
            mv.setView(new RedirectView(paymentFailedUrl));
            return mv;
        }
        logger.info("Rates have been obtained. Obtaining payment url.");
        PaymentRate payment = new PaymentRate();
        payment.setClientId(clientId);

        for (Rate rate : ratesData.get()) {

            this.separateIfIsNotEmpty(payment.getRateNumberStr());

            payment.getRateNumberStr().append(rate.getIdRata());

            this.separateIfIsNotEmpty(payment.getRateAmountStr());

            if (rate.getImportoPagato() == null) {
                rate.setImportoPagato(new BigDecimal(0));
            }
            BigDecimal amount = rate.getImportoRata().subtract(rate.getImportoPagato());
            String amountStr = this.buildPaymentAmount(amount);
            payment.getRateAmountStr().append(amountStr);
        }

        String paymentUrl = this.getPaymentRateUrl(payment);
        logger.info("Payment url has been obtained...");
        mv.setView(new RedirectView(paymentUrl));
        return mv;

    }

    private void separateIfIsNotEmpty(StringBuilder builder) {
        if (builder != null && builder.length() > 0) {
            builder.append(",");
        }
    }

    private String buildPaymentAmount(BigDecimal amount) throws PaymentException {
        try {
            String amountStr = StringUtils.replace(NumberFormat.getCurrencyInstance(Locale.ITALY).format(amount), ".", "");
            amountStr = StringUtils.replace(amountStr, ",", "");
            String encoded = URLEncoder.encode(amountStr, "UTF-8");
            String eurCharEncoded = URLEncoder.encode("\u20AC", "UTF-8");
            encoded = StringUtils.replace(encoded, eurCharEncoded, "");
            return URLDecoder.decode(encoded, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new PaymentException("Invalid Amount.");
        }
    }

    private String getPaymentUrl(Payment payment) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("Partnerstyret_id_INVOICE", CryptoUtils.aesEncrypt(payment.getClientId().toString(), aesEncryptKey));
        body.add("ListInvoice_INVOICE", CryptoUtils.aesEncrypt(payment.getInvoiceNumberStr().toString(), aesEncryptKey));
        body.add("Amount_INVOICE", CryptoUtils.aesEncrypt(payment.getInvoiceAmountStr().toString(), aesEncryptKey));
        body.add("ListInvoice_DATES", CryptoUtils.aesEncrypt(payment.getInvoiceDateStr().toString(), aesEncryptKey));
        body.add("ListInvoice_ID", CryptoUtils.aesEncrypt(payment.getInvoiceExternalIdsStr().toString(), aesEncryptKey));
        body.add("URL_OK_INVOICE", this.paymentRedirectUrl);
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        HttpEntity<String> response = restTemplate.exchange(paymentUrl, HttpMethod.POST, entity, String.class);
        return this.getPaymentRedirectLocationUrl(response.getHeaders().get("Location"));
    }


    private String getPaymentRateUrl(PaymentRate payment) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("CustomerID_RATA", CryptoUtils.aesEncrypt(payment.getClientId().toString(), aesEncryptKey));
        body.add("ListId_Rate", CryptoUtils.aesEncrypt(payment.getRateNumberStr().toString(), aesEncryptKey));
        body.add("ListAmount_Rate", CryptoUtils.aesEncrypt(payment.getRateAmountStr().toString(), aesEncryptKey));
        body.add("URL_OK_INVOICE", this.paymentRateRedirectUrl);
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        HttpEntity<String> response = restTemplate.exchange(paymentRateUrl, HttpMethod.POST, entity, String.class);
        return this.getPaymentRedirectLocationUrl(response.getHeaders().get("Location"));
    }

    @Override
    public PagamentiPaymentResponse getPaymentModalitaPagamentiUrl(PagamentiPaymentRequest body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.AUTHORIZATION, paymentCreditCardCredentials);
        body.setURLNotify(paymentOkUrl);
        body.setURLError(paymentKoUrl);
        body.setURLCallbackChiam(paymentcallbackUrl);
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Obtaining payment URL from remote service.");
        return restTemplate.exchange(paymentCartaCreditoUrl, HttpMethod.POST, entity, PagamentiPaymentResponse.class).getBody();
    }

    @Override
    public NexiPaymentResponse getNexiPaymentInfo(NexiPaymentRequest body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.AUTHORIZATION, paymentCreditCardCredentials);
        String url = paymentAutoricaricaOkUrl + body.getAddInfo3();
        body.setURL(url);
        body.setURL_Back(url);
        body.setAddInfo3("");
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Obtaining NEXI payment info from remote service");
        return restTemplate.exchange(paymentNexiUrl, HttpMethod.POST, entity, NexiPaymentResponse.class).getBody();
    }

    @Override
    public PagamentiPaymentResponse getAutoricaricaURL(PagamentiPaymentRequest body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.AUTHORIZATION, paymentCreditCardCredentials);
        body.setURLNotify(paymentAutoricaricaOkUrl + body.getAddInfo3() + "?success=true");
        body.setURLError(paymentAutoricaricaOkUrl + body.getAddInfo3() + "?success=false");
        body.setURLCallbackChiam(paymentAutoricaricaCallbackUrl);
        body.setAddInfo3("");
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Obtaining payment URL from remote service.");
        return restTemplate.exchange(paymentCartaCreditoUrl, HttpMethod.POST, entity, PagamentiPaymentResponse.class).getBody();
    }

    @Override
    public PayPalResponse getAutoricaricaPayPalURL(ActivatePayPalAutoricaricaRequest body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.AUTHORIZATION, paymentCreditCardCredentials);
        body.setURL_OK(paymentAutoricaricaOkUrl + body.getAddInfo3() + "?success=true");
        body.setURL_KO(paymentAutoricaricaOkUrl + body.getAddInfo3() + "?success=false");
        body.setUrlCallback(paymentAutoricaricaPaypalCallbackUrl);
        body.setAddInfo3("");
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Obtaining payment PayPal URL from remote service");
        return restTemplate.exchange(paymentAutoricaricaPayPalURL, HttpMethod.POST, entity, PayPalResponse.class).getBody();
    }

    @Override
    public CheckSubscriptionAutoricaricaResponse getInformationAboutAutoricarica(CheckSubscriptionAutoricaricaRequest body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.AUTHORIZATION, paymentCreditCardCredentials);
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Getting payment information from remote service.");
        return restTemplate.exchange(paymentAutoricaricaCheckSubscription, HttpMethod.POST, entity, CheckSubscriptionAutoricaricaResponse.class).getBody();
    }

    @Override
    public Map deactivateAutoricarica(DeactivateAutoRicaricaRequest body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.AUTHORIZATION, paymentCreditCardCredentials);
        HttpEntity<?> entity = new HttpEntity<Object>(body, headers);
        logger.info("Deactivating autoricarica.");
        return restTemplate.exchange(paymentDeactivateAutoricarica, HttpMethod.POST, entity, Map.class).getBody();
    }

    //  "Le fatture selezionate risultano già in pagamento"
    private String getPaymentRedirectLocationUrl(List<String> locations) {
        if (locations != null && !locations.isEmpty()) {
            String location = locations.get(0);
            if (locations.get(0).contains("error")) {
                return this.paymentFailedUrl + "?" + location.substring(location.indexOf("error"));
            }
            return locations.get(0);
        }
        return this.paymentFailedUrl;
    }
}
