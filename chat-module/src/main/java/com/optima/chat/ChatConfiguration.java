package com.optima.chat;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.io.*;

@Configuration
@ComponentScan
@ConditionalOnProperty("chat.ccmm.machine.address")
public class ChatConfiguration implements InitializingBean {

    private static final Logger logger = LogManager.getLogger(ChatConfiguration.class);

    @Override
    public void afterPropertiesSet() {

        InputStream serviceAccount = ChatConfiguration.class.getResourceAsStream("/firebase-admin/optima-838b9-firebase-adminsdk-panwe-b9b0641b4a.json");

        if (serviceAccount == null){
            logger.warn("File for firebase admin key not found");
            throw  new IllegalArgumentException("File for firebase admin key not found");
        }

        FirebaseOptions options = null;
        try {
            options = new FirebaseOptions.Builder()
                    .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                    .setDatabaseUrl("https://optima-838b9.firebaseio.com")
                    .build();
        } catch (IOException e) {
            e.printStackTrace();
        }
        FirebaseApp.initializeApp(options);
    }
}


