<div class="field-editor clearfix">
  <div class="form-block">
    <div [formGroup]="formGroup">
      <span *ngIf="!isEditing">{{formControl.value}}</span>
      <div class="checkbox-editor" *ngIf="isEditing&&options && options.length>1" (click)="openCheckboxDialog()">
        <span *ngIf="formControl.value">{{formControl.value}}</span>
        <span *ngIf="!formControl.value"
              class="checkbox-placeholder">Clicca per selezionare l'indirizzo da sostituire</span>
      </div>
    </div>
    <div [formGroup]="addressFromGroup">
      <app-form-input class="form-input" *ngIf="isEditing" [group]="addressFromGroup" name="toponimo"
                      placeholder="Toponimo"></app-form-input>
      <app-form-input class="form-input" *ngIf="isEditing" [group]="addressFromGroup" name="via"
                      placeholder="Nome via"></app-form-input>
      <app-form-input class="form-input" *ngIf="isEditing" [group]="addressFromGroup" name="civico"
                      placeholder="Civico"></app-form-input>
      <app-form-input class="form-input" *ngIf="isEditing" [group]="addressFromGroup" name="cap"
                      placeholder="CAP"></app-form-input>
      <app-form-input class="form-input" *ngIf="isEditing" [group]="addressFromGroup" name="comune"
                      placeholder="Comune"></app-form-input>
    </div>
  </div>
  <span *ngIf="!isEditing&&options&&options.length>1" class="fa fa-pencil edit-button" tooltip="Modifica"
        (click)="initializeCheckbox()"></span>
  <span *ngIf="!isEditing&&(!options||(options&&options.length===1))" class="fa fa-pencil edit-button" tooltip="Modifica"
        (click)="edit()"></span>
  <div *ngIf="isEditing" class="accept-decline-block">
    <span class="fa fa-remove" tooltip="annulla" (click)="closeEditor()"></span>
    <span class="fa fa-check accept-button" title="conferma" (click)="accept()"></span>
  </div>
  <div class="modal-window" *ngIf="showCheckboxDialog" [formGroup]="formGroup">
    <div class="modal-dialog modal-xs">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" aria-label="Close" (click)="closeCheckbox()">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title">Scegli l'indirizzo da sostituire
          </h4>
        </div>
        <div class="modal-body">
          <div *ngFor="let option of options;let id=index;">
            <div class="checkbox-item">
              <input [attr.id]="'option'+id" type="radio" [formControlName]="name" [value]="option"/>
              <label class="checkbox-label" [attr.for]="'option'+id">{{option}}</label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn accept-modal" (click)="acceptCheckboxDialog()">Conferma</button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="confirm-window" *ngIf="showConfirmDialog">
  <div class="modal-dialog modal-xs">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="showHideConfirmationPopup()">
          <span aria-hidden="true">&times;</span>
        </button>
        <!--<h4 class="modal-title">Sei sicuro di voler procedere con la variazione?</h4>-->
      </div>
      <div class="modal-body">
        <span
          class="confirm-message">Stai modificando l'indirizzo da <b>{{initialValue}}</b> a
        <b>{{addressFromGroup.controls['toponimo'].value}} {{addressFromGroup.controls['via'].value}} {{addressFromGroup.controls['civico'].value }}
          {{addressFromGroup.controls['cap'].value}} {{addressFromGroup.controls['comune'].value }}</b>. Per completare l'operazione clicca su "Conferma"?
        </span>
      </div>
      <div class="modal-footer">
        <button *ngIf="addressFromGroup.valid" class="btn accept-modal" (click)="confirm()">Conferma</button>
        <button class="btn decline-modal" (click)="showHideConfirmationPopup()">Annulla</button>
      </div>
    </div>
  </div>
</div>


