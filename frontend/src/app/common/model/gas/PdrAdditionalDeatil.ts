export interface PdrAdditionalDeatil {
  punto: string;
  idfatturazione: number;
  ambitotariffario: string;
  fasciaclimatica: string;
  zona: string;
  tipoutilizzo: string;
  tipologiautilizzoaeg: string;
  descrizioneAeeg: string;
  classificazionepdr: string;
  mercato: string;
  pcs: string;
  coefficientec: string;
  sottotipo: string;
  profilo: string;
  classeUtenza?: null;
  aliquotaIva: string;
  imposte?: null;
  distributoriLocali: string;
  numero: string;
  descDistributore: string;
  shipper?: null;
  classePrelievo: string;
  categoriaUso: string;
  periodicitafatturazione: string;
  fatturazioneProva: string;
  descrizioneAmbitoTariffario: string;
  descrizioneFasciaClimatica: string;
  nomeZona: string;
  codiceClassificazionePdr: string;
  tipoUtilizzoDescrizione: string;
  profiloUtilizzoDescrizione: string;
  cuCodice: string;
  optDescrizione: string;
  optCodiceCatUso: string;
  optDescrizioneCatUso: string;
  descrizioneCatUsoConcat: string;
  descrizioneIva: string;
  idProdottoDescrizione: string;
  periodFattDesc: string;
  nomePrecGest: string;
  idPrecGestNew: string;
  codiceProfPrel: string;
  codiceProfPrelId: string;
  shipperName?: null;
  classeContatoreDescrizione: string;
  usoEsclusivoPromiscuo?: null;
  usoPromiscuo?: null;
  motivazione: string;
  classeEsclusa?: null;
  pdrValore: string;
  motivazionedescrizione: string;
  usoPromiscuoDesc?: null;
  codiceProfPrelStr: string;
  ccuuid: string;
  sedeOperativa?: (SedeOperativaEntity)[] | null;
  idprodotto: string;
}
export interface SedeOperativaEntity {
  tipoIndirizzo?: null;
  indirizzo: string;
  comune: string;
  civico?: null;
  locazione?: null;
  tipoSede?: null;
  cap: string;
  idSede: string;
  provinciaDescrizione: string;
  provincia: string;
}
