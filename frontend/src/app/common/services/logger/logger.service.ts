import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {EventModel, LoggerRequestBody} from '../../model/logger/logger.model';

@Injectable()
export class LoggerService {

  readonly url = 'api/logger';

  element = new EventModel();
  loggerRequestBody = new LoggerRequestBody();
  loggerList = [];
  log = 'Enter the page Add Options Richiedi Portabilita';
  logFromAgentVisitBooking = 'Enter the page PRENOTA VISITA AGENTE';

  constructor(private http: HttpClient) {
  }

  public logEvent(event: Event) {

    if (event instanceof Event) {
      this.element.elementType = event.target['type'];
      this.element.elementName = event.target['name'];
      this.element.elementValue = event.target['value'] ? event.target['value'] : event.target['type'];
      this.log = this.element.elementName.concat(' input field was filled with the ')
        .concat(this.element.elementValue).concat(' value;');
    }
    if (event instanceof Date) {
      const formatData = this.getFormatData(event);
      this.log = ('Data di attivazione del servizio di portabilita input field was filled with the ')
        .concat(formatData.toString()).concat(' value;');
    }
    this.loggerList.push(this.log);
    if (this.element.elementType === 'submit') {
      this.postLogs();
    }
  }

  public logIncident(message: string) {
    this.loggerList.push(message);
    this.postLogs();
  }

  private getFormatData(date: Date) {
    const Day = date.getDate() < 10 ? '0'.concat(String(date.getDate())) : date.getDate();
    const Month = (date.getMonth() < 9 ? '0'.concat(String(date.getMonth() + 1)) : date.getMonth() + 1);
    const Year = date.getFullYear();
    return Day + '/' + Month + '/' + Year;
  }

  public logVisitAgentInformation(event: Event) {
    this.element.elementType = event.target['type'];
    this.element.elementName = event.target['name'];
    this.element.elementValue = event.target['value'] ? event.target['value'] : event.target['type'];
    if (event.target['type'] === 'select-one') {
      this.logFromAgentVisitBooking = `Topic of conversation: ${this.element.elementValue}.`;
      this.loggerList.push(this.logFromAgentVisitBooking);
    } else if (event.target['type'] === 'text') {
      this.logFromAgentVisitBooking = `The reason for visiting the agent: ${this.element.elementValue}`;
      this.loggerList.push(this.logFromAgentVisitBooking);
    }
    if (this.element.elementType === 'submit') {
      this.postLogs();
      this.loggerList = [];
    }
  }

  private postLogs() {
    this.loggerRequestBody.logList = this.loggerList;
    this.loggerRequestBody.clientId = localStorage.getItem('clientId');
    this.http.post(this.url, this.loggerRequestBody).toPromise();
    this.loggerList = [];
  }

}
