<nav class="navbar topnavbar" role="navigation">
  <div class="side-menu-icon" [ngClass]="{'side-menu-icon-cyan': index%2===1 }" (click)="indexIncrement()">
  </div>
  <div class="navbar-header">
    <a class="navbar-brand" [routerLink]="['/home/<USER>']">
      <div class="brand-logo">
        <img class="img-responsive" src="assets/img/logo/optima_new_main_logo.svg" alt="App Logo" (click)="goHome()">
      </div>
    </a>
  </div>
  <div class="notification-block">
    <app-client-notification></app-client-notification>
    <div class="logout" (click)="logout()"></div>
  </div>
</nav>
<app-e-mail class="email-button"></app-e-mail>
<app-optima-chat [chatUser]="chatUser"></app-optima-chat>
<app-notification-component [toast]="toast | async"></app-notification-component>
<app-dialog-modal-wrapper></app-dialog-modal-wrapper>
<app-notifica-mdp [mdpInfo]="notificaMdpInfo"></app-notifica-mdp>
<app-modal-wrapper></app-modal-wrapper>
