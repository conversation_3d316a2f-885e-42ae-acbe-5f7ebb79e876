.container-fluid {
  margin-top: 7%;
}

.panel.panel-default {
  width: 100%;
  margin: auto;

}
.right {
  float: right;
  padding: 0;
}

.panel {
  padding: 15px 0;
}

.panel.panel-default {
  width: 100%;
  margin: auto;

}

.view img {
  width: 100%;
  padding: 0 15px 15px 15px;
}

.row {
  margin: auto;
}

.user-data-table-block {
  border: 1px solid #b6cce3;
  border-radius: 10px;
  .material {
    border-radius: 10px;
  }
  .title {
    padding-top: 10px;
    padding-bottom: 10px;
    font-weight: bold;
    color: #e54c36;
    border-bottom: 2px solid #e0e0e0;
  }
  .detail-row {
    display: inline-flex;
    padding-right: 0;
    padding-left: 0;
    div {
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .data {
      font-size: 14px;
      font-weight: 400;
    }
    .subject {
      font-weight: bold;
      border-right: 2px solid #b6cce3;
      padding-right: 0;
    }
    &:nth-child(2n+2) {
      background-color: #f0f5f9
    }
    &:nth-child(2n+3) {
      background-color: #ffffff
    }
    &:nth-child(2n+3) {
      border-bottom: 2px solid #b6cce3
    }
    &:last-child {
      border-bottom: none;
      border-bottom-right-radius: 10px;
      border-bottom-left-radius: 10px;
    }
  }
}

.confirm-message {
  color: #36749d;
}

.fa-file-pdf-o, .fa-remove {
  float: right;
  margin-right: 5%;
  cursor: pointer;
  font-size: 18px;
  color: #e54c36;
}

.modifica {
  background: url("../../../../../assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

.icon {
  float: left;
  width: 65px;
  height: 65px;
  margin: 10px 15px 0 auto;
}

.app--btn-dropdown {
  padding-right: 0 !important;
  padding-left: 0 !important;
  width: 0;
  border: none;
  background: none !important;
}

.icons {
  margin: auto;
}

@media screen and (max-width: 991px) {
  .view img {
    width: 100%;
    padding: 0 0 15px 0;
  }

  .user-data-block {
    padding: 0;
  }
  .user-data-table-block {
    .title {
      display: none;
    }
    .detail-row {
      display: block;
      .data {
        min-height: 41px;
      }
      &:nth-child(2n+3) {
        border: none;
      }
      &:nth-child(2n+3) {
        border-bottom: 2px solid #b6cce3
      }
      &:nth-child(2) {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        .subject {
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
      }
      &:last-child {
        border-bottom: none;
        .data {
          border-bottom-right-radius: 10px;
          border-bottom-left-radius: 10px;
        }
      }
      &:nth-child(2n+2) {
        background-color: #ffffff
      }
      div {
        &:nth-child(1) {
          background-color: #f0f5f9
        }
        &:nth-child(2) {
          background-color: #ffffff
        }
      }
      .subject {
        border-right: none;
      }
    }
  }
}


@media screen and (max-width: 980px) {
  .container-fluid {
    padding: 40px 0 0 0;
    margin-top: 0;
  }
}


@media only screen and (max-width: 800px) {
  .container-fluid {
    float: left;
    width: 100%;
  }
  .panel-body {
    padding: 15px 5px 5px 5px;
  }
}
@media only screen and (max-width: 400px) {

  .panel-body {
    padding: 0;
  }
}
