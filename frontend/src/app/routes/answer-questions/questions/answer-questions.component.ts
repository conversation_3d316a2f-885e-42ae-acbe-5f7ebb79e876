import {Component, OnInit} from '@angular/core';
import {NavigationEnd, NavigationStart, Router} from '@angular/router';
import {answerQuestionMobileLayoutOrdersMap} from '../config/config';
import {ConstantUtil} from '../../../utils/ConstantUtil';


@Component({
  selector: 'app-answer-questions',
  templateUrl: './answer-questions.component.html',
  styleUrls: ['./answer-questions.component.scss']
})
export class AnswerQuestionsComponent implements OnInit {

  isMobile = ConstantUtil.isMobile;

  mobileLayoutOrder: number;

  lastRouteUrl: string;

  constructor(private router: Router) {
    router.events.subscribe((val) => {
      if (val instanceof NavigationEnd) {
        this.lastRouteUrl = val.url;
      }
    });
  }

  switchOpenClose(routeUrl: string) {
    if (this.isMobile) {
      if (this.lastRouteUrl === routeUrl) {
        this.router.navigateByUrl('/support/questions');
      }
    }
  }


  ngOnInit() {
    this.mobileLayoutOrder = answerQuestionMobileLayoutOrdersMap[this.router.url];
    this.router.events.subscribe((router: NavigationStart) => {
      this.mobileLayoutOrder = answerQuestionMobileLayoutOrdersMap[router.url];
    });
  }

}
