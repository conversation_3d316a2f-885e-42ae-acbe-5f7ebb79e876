import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { select } from '@angular-redux/store';
import { Observable } from 'rxjs/Observable';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import PodDetailsRequest, { PodRequest } from '../../../../common/model/services/PodDetailsRequest';
import { ServiziAttiviService } from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import { OptimaIconUtils } from '../../../../common/utils/OptimaIconUtils';
import { Utility } from '../../../../common/model/services/userServices.model';
import { HomeService } from '../../../home/<USER>/home/<USER>';
import { UserData } from '../../../../common/model/userData.model';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import { ServicesActions } from '../../../../redux/services/actions';

@Component({
  selector: 'app-energia-layout',
  templateUrl: './energia-layout.component.html',
  styleUrls: ['./energia-layout.component.scss']
})
export class EnergiaLayoutComponent implements OnInit, OnDestroy {
  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  userCluster: string;

  podDetails: object;

  pod: string;

  pdf: Array<any>;

  utenza: Utility = {} as Utility;

  serviceDataSubscription: Subscription;

  constructor(private route: ActivatedRoute, private serviziAttiviService: ServiziAttiviService,
              private optimaIconUtils: OptimaIconUtils, private homeServices: HomeService,
              private servicesActions: ServicesActions) {
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
        this.pdf = this.setPDFList('ENERGIA');
      }
    });

    this.route.params.subscribe(params => {
      if (params.pod) {
        this.pod = params.pod;
        const request = new PodDetailsRequest();
        request.mostraStoria = true;
        request.podRequests = [{clientId: localStorage.getItem('clientId'), pod: params.pod as string}] as PodRequest[];
      }
      return Observable.empty();
    });
    this.serviceData.subscribe(service => {
      const {lucePodDetails, activeServices, servicesLoaded} = service;
      if (servicesLoaded) {
        this.servicesActions.loadLucePodDetailsIfNotExist('ENERGIA');
      }
      this.podDetails = lucePodDetails;
      Object.keys(activeServices).forEach(key => {
        activeServices[key].utilities.forEach(utility => {
          if (utility.utNumber === this.pod) {
            this.utenza = utility;
          }
        });
      });
    });
  }

  ngOnInit() {
  }

  serviceIcon(icon: string): string {
    return this.optimaIconUtils.getServiceIconByName(icon);
  }

  getName(name) {
    return this.serviziAttiviService.getNumberName(name);
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }

  ngOnDestroy(): void {
    if (this.serviceDataSubscription) {
      this.serviceDataSubscription.unsubscribe();
    }
  }

}
