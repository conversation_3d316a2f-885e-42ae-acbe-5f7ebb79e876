<div class="col-md-12 block-md-12 outBox">
  <div class="row app--header-block">
    <div class=" block-md-3 topBlock head"
         [ngClass]="[serviceIcon('GAS'), 'service-icon']"></div>
    <div class="serviceName">GAS</div>
  </div>
  <div class="mainBlock">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <p><b>Stato:</b> {{utenza.status}}</p>
        <p><b>{{getName('GAS')}} </b> {{utenza.utNumber}}</p>
        <p *ngIf="podDetails && podDetails.length>0" class="detailRow"><b>Tipologia d'uso: </b>
          {{podDetails[0].tipoPdrDescrizione}}
        </p>
        <div *ngIf="pdrAdditionalDetails && pdrAdditionalDetails[utenza.utNumber]">
          <p
            *ngIf="pdrAdditionalDetails[utenza.utNumber].sedeOperativa&& pdrAdditionalDetails[utenza.utNumber].sedeOperativa.length>0"
            class="detailRow"><b>Indirizzo fornitura: </b>
            {{pdrAdditionalDetails[utenza.utNumber].sedeOperativa[0].indirizzo}},
            {{pdrAdditionalDetails[utenza.utNumber].sedeOperativa[0].comune}},
            {{pdrAdditionalDetails[utenza.utNumber].sedeOperativa[0].provincia}},
            {{pdrAdditionalDetails[utenza.utNumber].sedeOperativa[0].cap}}
          </p>
          <p *ngIf="podDetails && podDetails.length>0" class="detailRow"><b>Tipo Contatore: </b>
            {{podDetails[0].descrizioneContatore}}
          </p>
        </div>
      </div>
    </div>
    <div class="row ">
      <div class="col-lg-9">
        <div>
          <p *ngIf="utenza.status==='ATTIVATO'"><b>Data Attivazione:</b> {{utenza.startDate | date : 'dd/MM/yyyy'}}
          </p>
          <!--<p *ngIf="utenza.status==='IN_ATTIVAZIONE'"><b>Data Prevista Attivazione:</b> {{utenza.firstActivationDate |
            date : 'dd/MM/yyyy'}}</p>-->
        </div>
      </div>
    </div>

    <div *ngIf="pdf" class="app-drop-down">
      <a mat-button class="dropdown dontShow" [matMenuTriggerFor]="menu" [ngClass]="{ 'disabled' :condition }">
        <div class="icon modifica"></div>
      </a>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below" [overlapTrigger]="false">
        <div class="mat-menu-style">
        <button mat-menu-item *ngIf="pdf.length===0">
          <span> No PDF </span>
        </button>
<!--        <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--          Variazioni e richieste-->
<!--        </button>-->
        <button mat-menu-item class="menu-button odds-bg" *ngFor="let pdfRow of pdf">
          <span class="icon-pdf-load"> </span>
          <a target='_blank' href="{{pdfRow.link}}">
            {{pdfRow.name}}</a>
        </button>
        </div>
      </mat-menu>
    </div>

  </div>
  <div class="serviziButton">
    <a class="elenco" routerLink='/faidate/servizi-attivi'>VAI ALL’ELENCO DEI TUOI SERVIZI</a>
  </div>
</div>
