import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {Subscription} from 'rxjs/Subscription';
import {Utility} from '../../../../common/model/services/userServices.model';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';

@Component({
  selector: 'app-safe-call-layout',
  templateUrl: './safe-call-layout.component.html',
  styleUrls: ['./safe-call-layout.component.scss']
})
export class SafeCallLayoutComponent implements OnInit, OnDestroy {
  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  serviceDataSubscription: Subscription;
  safeCallInfo: Utility;
  listOfNumbers: string[];
  listOfUtilities: Array<Utility> = [];
  formGroup: FormGroup;

  constructor(private fb: FormBuilder) {
    this.serviceDataSubscription = this.serviceData.subscribe(serviceState => {
      serviceState.services.forEach(service => {
        if (service.serviceName === 'SafeCall') {
          this.listOfUtilities = service.utilities.map(item => item);
          this.safeCallInfo = service.utilities[0];
          this.listOfNumbers = service.utilities.map((utility: any) => utility.additionalInfo.msisdn);
        }
      });
    });
    this.formGroup = this.fb.group({
      number: [null, [Validators.required]]
    });
  }

  ngOnInit() {
  }

  ngOnDestroy(): void {
    if (this.serviceDataSubscription) {
      this.serviceDataSubscription.unsubscribe();
    }
  }

  switchNumber() {
    this.safeCallInfo = this.listOfUtilities.find(item => item.additionalInfo.msisdn === this.formGroup.value.number.replace('SIM ', ''));
  }
}
