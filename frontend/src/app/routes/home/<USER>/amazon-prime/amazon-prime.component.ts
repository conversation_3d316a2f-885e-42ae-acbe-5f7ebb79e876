import {AfterViewInit, Component, OnInit} from '@angular/core';
import {AmazonPrimeService} from '../../../../common/services/amazonprime/amazon-prime.service';
import {Router} from '@angular/router';
import {DialogModalActions} from "../../../../redux/dialogModal/actions";

@Component({
  selector: 'app-amazon-prime',
  templateUrl: './amazon-prime.component.html',
  styleUrls: ['./amazon-prime.component.scss']
})
export class AmazonPrimeComponent implements OnInit, AfterViewInit {

  titleText: string;

  subTitleText: string;

  buttonText: string;

  termsLabelText: string;

  isMobile = window.innerWidth <= 991;

  constructor(private dialogModalActions: DialogModalActions, private amazonPrimeService: AmazonPrimeService,
              private router: Router) {
    amazonPrimeService.getAmazonPrimeData(localStorage.getItem('clientId')).subscribe(res => {
      if (res.descrizioneEsito === 'KO') {
        this.router.navigate(['/home/<USER>'])
      }
    });
    amazonPrimeService.getAmazonPrimeData(localStorage.getItem('clientId')).subscribe(data => {
      if (data.presenzaPromozione) {
        this.titleText = 'Con Optima hai Amazon Prime incluso per un anno!';
        this.buttonText = 'REGISTRATI';
        this.termsLabelText = 'Cliccando su REGISTRATI accetti il';
      } else {
        this.titleText = 'Tutti i vantaggi di Amazon Prime comodamente in bolletta!';
        this.subTitleText = 'Con Optima puoi averlo per un anno con piccole rate mensili direttamente in bolletta.';
        this.buttonText = 'ATTIVALO SUBITO';
        this.termsLabelText = 'Cliccando su ATTIVALO SUBITO accetti il';
      }
    });

  }

  ngOnInit() {
  }

  ngAfterViewInit(): void {
  }
}
