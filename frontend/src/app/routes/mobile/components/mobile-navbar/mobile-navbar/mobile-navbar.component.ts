import { Component, OnInit } from '@angular/core';
import MenuItem from '../../../../../common/model/MenuItem';
import { menu } from '../../../menu';

@Component({
  selector: 'app-mobile-navbar',
  templateUrl: './mobile-navbar.component.html',
  styleUrls: ['./mobile-navbar.component.scss']
})
export class MobileNavbarComponent implements OnInit {

  menuItems: Array<MenuItem> = [];

  constructor() {
    this.menuItems = menu;
  }

  ngOnInit() {
  }


}
