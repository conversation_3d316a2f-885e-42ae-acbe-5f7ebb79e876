import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import * as moment from 'moment';
import {InvoiceService} from '../../../../invoices/invoice.service';
import {UserData} from '../../../../../common/model/userData.model';
import {UserDataService} from '../../../../../common/services/user-data/userData.service';
import {PayPalService} from '../../../../../common/services/paypal/pay-pal-service.service';
import {ActivatedRoute} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import {ContractRecord} from '../../../../../common/model/mobile/contract-record/ContractRecord';
import {select} from '@angular-redux/store';
import {MobileService} from '../../../../../common/services/mobile/mobile.service';
import Validator from '../../../../../common/utils/Validator';
import {FormUtils} from '../../../../../common/utils/FormUtils';


@Component({
  selector: 'app-top-up-sim-with-paypal',
  templateUrl: './top-up-sim-with-paypal.component.html',
  styleUrls: ['./top-up-sim-with-paypal.component.scss']
})
export class TopUpSimWithPaypalComponent implements OnInit {

  formGroupPayPal: FormGroup;
  payPalActivation: any;
  userData: UserData;
  amounts: Array<number> = [5, 10, 15, 20, 50];
  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;
  contractRecordArray: Array<ContractRecord>;

  constructor(private service: InvoiceService, userService: UserDataService, private payPalService: PayPalService, private route: ActivatedRoute,
              private mobileService: MobileService, protected formBuilder: FormBuilder) {
    userService.getUserData().subscribe(item => this.userData = item);
    mobileService.loadContractRecords(localStorage.getItem('clientId')).subscribe(item => this.contractRecordArray = item);
    this.formGroupPayPal = this.buildForm();
  }

  ngOnInit(): void {
  }

  buildForm() {
    const formGroup = this.formBuilder.group({
      mooneyValue: new FormControl(5, Validators.required),
      msisdnId: new FormControl(null),
      optimaNumber: new FormControl(null, [Validators.required, Validator.withLength(6, 10)])
    });
    this.setCommonFormGroupConfiguration(formGroup);
    return formGroup;
  }

  setCommonFormGroupConfiguration(formGroup: FormGroup) {
    formGroup.controls.msisdnId.valueChanges.subscribe((input: string) => {
      if (input) {
        formGroup.controls.optimaNumber.disable({emitEvent: false});
      } else {
        formGroup.controls.optimaNumber.enable({emitEvent: false});
      }
    });

    formGroup.controls.optimaNumber.valueChanges.subscribe((input: string) => {
      if (input) {
        formGroup.controls.msisdnId.disable({emitEvent: false});
      } else {
        formGroup.controls.msisdnId.enable({emitEvent: false});
      }
    });

    this.route.params.subscribe(param => {
      if (param.id) {
        formGroup.controls.msisdnId.setValue(parseInt(param.id, 10));
      }
    });
  }

  checkForm() {
    if (!this.formGroupPayPal.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupPayPal);
    } else {
      const number = this.formGroupPayPal.value.msisdnId ? this.formGroupPayPal.value.msisdnId : '39' + this.formGroupPayPal.value.optimaNumber;
      this.payPalService.isOptimaNumber(number).subscribe(
        resp => {
        if (resp === true) {
          this.formGroupPayPal.controls['optimaNumber'].setErrors({'numberExistence': true});
          return;
        } else {
          this.payPalActivation = {
            'TipoPagamento': 'Paypal',
            'SistemaChiamante': 'Selfcare',
            'Data': moment(Date.now()).format('DD/MM/YYYY'),
            'TotalePagamento': +this.formGroupPayPal.value.mooneyValue,
            'Ricariche': [{
              'ChannelId': '1',
              'Importo': +this.formGroupPayPal.value.mooneyValue,
              'NumeroTelefono': number,
              'SubscriptionID': this.getIncidentId(this.contractRecordArray, +number)
            }],
            'CodiceCliente': this.userData.id,
            'RagioneSociale': this.userData.nameInInvoice,
            'CF': this.userData.fiscalCode,
            'PIVA': this.userData.vatNumber
          };
          this.payPalService.postPayPalActivationRicarica(this.payPalActivation).subscribe(item =>
            window.open(item.Return_URL));
        }
      });
    }
  }

  getIncidentId(contractRecords: Array<ContractRecord>, msisdnId: number) {
    for (const item of contractRecords) {
      if (item.msisdnId === msisdnId) {
        return item.id;
      }
    }
  }
}
