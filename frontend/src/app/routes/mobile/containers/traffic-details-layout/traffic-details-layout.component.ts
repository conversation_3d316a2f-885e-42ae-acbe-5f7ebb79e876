import { Component, OnInit } from '@angular/core';
import * as moment from 'moment';
import { MobileService } from '../../../../common/services/mobile/mobile.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormUtils } from '../../../../common/utils/FormUtils';
import TariffDetail from '../../../../common/model/mobile/TariffDetail';
import TariffDetailsRequest from '../../../../common/model/mobile/TariffDetailsRequest';
import { TariffDetailsService } from '../../service/tarriff-details/tariff-details.service';
import Validator from '../../../../common/utils/Validator';
import { ContractRecord } from '../../../../common/model/mobile/contract-record/ContractRecord';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { select } from '@angular-redux/store';

@Component({
  selector: 'app-traffic-details-layout',
  templateUrl: './traffic-details-layout.component.html',
  styleUrls: ['./traffic-details-layout.component.scss']
})
export class TrafficDetailsLayoutComponent implements OnInit {

  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;

  formGroup: FormGroup;

  tariffTypes: Array<string>;

  referencePeriods = [];

  dateRangeValue = '';

  tarriffDetais: Array<TariffDetail> = [];

  constructor(private mobileService: MobileService, private formBuilder: FormBuilder,
              private tariffDetailsService: TariffDetailsService, private route: ActivatedRoute) {

    this.formGroup = this.formBuilder.group({
      msisdnId: [null, [Validators.required]],
      tariffType: [null, [Validators.required]],
      referencePeriod: [null, Validator.requiredIfFieldIsNotSet('dateRange')],
      dateRange: [null, Validator.requiredIfFieldIsNotSet('referencePeriod')]
    });

    Validator.listenOtherInputAndSetUpdateWithoutEmitting(this.formGroup, 'dateRange', ['referencePeriod'], null);
    Validator.listenOtherInputAndSetUpdateWithoutEmitting(this.formGroup, 'referencePeriod', ['dateRange'], null);

    this.referencePeriods = this.tariffDetailsService.getReferencePeriods();
    this.tariffTypes = this.tariffDetailsService.getTariffTypesArray();
    this.route.params.subscribe(param => {
      if (param.id) {
        this.formGroup.controls.msisdnId.setValue(parseInt(param.id, 10));
      }
    });

    // Fixing known bug with date format
    this.formGroup.controls.dateRange.valueChanges.subscribe(value => {
      this.dateRangeValue = value && value.length > 0 ?
        `${moment(value[0]).format('DD/MM/YYYY')}-${moment(value[1]).format('DD/MM/YYYY')}` : '';
    });
  }

  ngOnInit() {
  }

  loadTariffDetails() {
    if (this.formGroup.valid) {
      const tariffDetailsRequest = new TariffDetailsRequest();
      const formData = this.formGroup.value;
      tariffDetailsRequest.msisdnId = formData.msisdnId;
      this.tariffDetailsService.setDateRangeAccordingToReferencePeriodIfExist(tariffDetailsRequest, formData.referencePeriod);
      this.tariffDetailsService.setDateRangeAccordingToDateRangeFormDataIfExist(tariffDetailsRequest, formData.dateRange);
      this.mobileService.loadTariffDetails(tariffDetailsRequest).subscribe((data: Array<TariffDetail>) => {
        this.tarriffDetais = this.tariffDetailsService.filterByType(data, formData.tariffType);
        if (formData.tariffType === 'Dati / Servizi a contenuto') {
          this.tarriffDetais = this.tarriffDetais.filter(value => (!(value.usedMegabytes === 200 && value.duration === 0 && value.amount === 0)));
          this.tarriffDetais = this.convertToGigabytes();
        }
      });
    } else {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    }
  }

  convertToGigabytes() : Array<TariffDetail> {
    this.tarriffDetais.forEach(val => {
      if (val.usedMegabytes >= 1024) {
        val.usedMegabytes = val.usedMegabytes / 1024;
        val.usedMegabytes = Number.parseFloat(val.usedMegabytes.toFixed(3));
        val.suffix = 'GB';
      } else val.suffix = 'MB';
      val.usedMegabytes = this.replaceDots(val.usedMegabytes);
    });
    return this.tarriffDetais;
  }

  replaceDots(value) : number {
    if (value) {
      return value.toString().replace('.', ',')
    }
  }

}
