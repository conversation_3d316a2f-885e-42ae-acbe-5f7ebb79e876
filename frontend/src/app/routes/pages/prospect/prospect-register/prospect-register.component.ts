import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {ProspectUserService} from '../../../../common/services/prospect-user/prospect-user.service';
import ProspectUserRegisterForm from '../../../../common/model/prospect-user/prospectUser';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Router} from '@angular/router';

@Component({
  selector: 'app-register-component',
  templateUrl: './prospect-register.component.html',
  styleUrls: ['./prospect-register.component.scss']
})
export class ProspectRegisterComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  formGroup: FormGroup;
  showSussesModalWindow: boolean;
  showErrorCfAlreadyRegistered: boolean;
  showErrorNotFoundEmailOrContract: boolean;
  showAlreadyRegisterModalWindow: boolean;

  constructor(private formBuilder: FormBuilder, private registerService: ProspectUserService, private router: Router) {
    this.formGroup = this.formBuilder.group({
      CF: ['', Validators.required],
      emailOrContractId: ['', Validators.required],
      password1: ['', Validators.required],
      password2: ['', Validators.required]
    }, {
      validator: this.matchValidator.bind(this)
    });
    this.formGroup.get('CF').valueChanges.subscribe(() => {
      if (this.showErrorCfAlreadyRegistered) {
        this.showErrorCfAlreadyRegistered = false;
      }
    });
    this.formGroup.get('emailOrContractId').valueChanges.subscribe(() => {
      if (this.showErrorNotFoundEmailOrContract) {
        this.showErrorNotFoundEmailOrContract = false;
      }
    });
  }

  matchValidator(group: FormGroup) {
    if (group.controls['password1'].value === group.controls['password2'].value) {
      return null;
    }
    return {
      mismatch: true
    };
  }

  ngOnInit() {
  }

  checkForm() {
    if (!this.formGroup.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    } else {
      this.registerService.getContractsByCodiceFiscale(this.formGroup.value.CF).subscribe(information => {
        let foundItem = false;
        for (const item of information.response) {
          if (item.dataStipula >= new Date('2024-01')) {
            this.sendForm(item.email, item.idContratto);
            foundItem = true;
            break;
          }
        }
        if (!foundItem) {
          this.showErrorNotFoundEmailOrContract = true;
        }
      });
    }
  }

  sendForm(email: string, contractId: number) {
    const registerForm = new ProspectUserRegisterForm(this.formGroup.value.CF, email, contractId,
      this.formGroup.value.password1, this.formGroup.value.password2);
    this.registerService.sendRegisterForm(registerForm).subscribe(registerResponse => {
      if (registerResponse.status === 200) {
        this.showSussesModalWindow = true;
      }
    }, (error) => {
      if (error.status === 400) {
        this.showErrorCfAlreadyRegistered = true;
      }
    });
  }

  hideModalWindow() {
    this.showSussesModalWindow = false;
  }

  openLoginPage() {
    const URL = this.showAlreadyRegisterModalWindow ? '/login' : '/prospect/login';
    this.router.navigateByUrl(URL).catch(() => {
    });
  }
}
