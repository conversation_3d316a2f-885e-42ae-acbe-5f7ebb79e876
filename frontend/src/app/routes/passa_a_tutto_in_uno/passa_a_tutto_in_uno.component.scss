.container-fluid {
  margin-top: 7%;
  padding: 0;

}

.col-lg-12 {
  width: 100%;
  margin: 0;
  padding-left: 0;
  padding-right: 0;

}

.panel-body {
  padding: 15px 20%;
}

.imgTop {
  text-align: center;
  background: url("../../../assets/img/passa-a-tiu/matrioski_tutto-in-uno_servizi.jpg") no-repeat center;
  height: 350px;
  background-size: contain;
}

.title {
  color: #e54c36;
  padding:20px 0;
  text-align: center;
}
.center{
  text-align: center;
}
.text {
  color: #36749d;
  text-align: center;
}
.small{
  font-size: 10px;
}
.imgBlock {
  height: 300px;
  text-align: center;
  background: url("../../../assets/img/passa-a-tiu/Conto-relax.jpg") no-repeat center;
  background-size: contain;
  float: left;
}

.minus {
  height: 25px;
  text-align: center;
  background: url("../../../assets/img/passa-a-tiu/contorelax-meno.svg") no-repeat center;
  background-size: contain;
  float: left;
}

.plus {
  height: 50px;
  text-align: center;
  background: url("../../../assets/img/passa-a-tiu/contorelax-piu.svg") no-repeat center;
  background-size: contain;
  float: left;
}

.smallfooter {
  margin: 30px 0;
  padding: 10px 0 40px 0;
  background-color: gainsboro;
  border-radius: 5px;
  text-align: center;
}

.red {
  color: #e54c36;
}
.blue {
  color: #36749d;
}

.btn {
  text-align: center;
  margin: auto;
  padding: 4px 7px;
  width: 30%;
}

.success {
  background-color: #387d25;
  color: white;
}
.justify{
  text-align: justify;
}
.quindi{
  padding: 30px 0;
}
@media only screen and (max-width: 991px) {
  .container-fluid {
    width: 100%;
    padding: 0;
  }
  .panel-body {
    padding: 45px 20px;
  }
}

@media only screen and (max-width: 800px) {

  .container-fluid {
    float: left;
    width: 100%;
  }

}

@media only screen and (max-width: 600px) {
  .btn {
    width: 50%;
  }
  .panel-body {
    padding: 15px 20px;
  }

}
