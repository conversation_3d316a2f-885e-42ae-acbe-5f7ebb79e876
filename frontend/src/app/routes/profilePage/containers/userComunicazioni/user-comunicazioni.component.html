<div class="col-lg-9 col-md-9 col-sm-12 col-xs-12 communication-layout">

  <div class="col-lg-12 col-md-12 page-title">
    <div class="title-image"></div>
    <div class="text">LE TUE COMUNICAZIONI</div>
  </div>

  <div *ngIf="!communicationData?.length" class="col-lg-12 col-md-12 no-result">
    <h4>Non risulta alcuna comunicazione associata al tuo codice cliente.</h4>
  </div>
  <div *ngIf="communicationData?.length">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 navigation-block">
      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 ricerca"><b>Ricerca comunicazioni</b></div>
      <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6">
        <label>Data:</label>
        <input class="input-date" type="date" (change)="applyFilter($event.target.value)"
               placeholder="data"/>
      </div>
      <!--      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3">-->
      <!--        <button class="">CERCA</button>-->
      <!--      </div>-->
    </div>

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 communication-result">
      <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 no-padding"></div>
      <div class="col-lg-7 col-md-7 col-sm-7 col-xs-5 header no-padding">Comunicazioni</div>
      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-4 header text-center no-padding">Data</div>
      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 header no-padding">Scarica</div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-bottom-line"></div>

      <div *ngFor="let item of paginator | async"
           class="col-lg-12 col-md-12 col-sm-12 col-xs-12 communication-row no-padding">
        <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 no-padding">
          <label class="checkbox-container">
            <!--<input name="informationPrivacy" type="checkbox"
                   class="form-checkbox"/>
            <span class="mark"></span>-->
          </label>
        </div>
        <div class="col-lg-7 col-md-7 col-sm-7 col-xs-5 no-padding">{{item.subject}}</div>
        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-4 text-center no-padding">
          {{item.creationDate| date : "dd/MM/y"}}</div>
        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 no-padding">
          <a *ngIf="item.fileUrl" class="icon-pdf-load pull-left" [attr.href]="item.fileUrl"
             target="_blank"></a>
        </div>
      </div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 paginator">
      <app-paginator [items]="communicationTableData" [listener]="paginator"></app-paginator>
    </div>
  </div>
</div>
