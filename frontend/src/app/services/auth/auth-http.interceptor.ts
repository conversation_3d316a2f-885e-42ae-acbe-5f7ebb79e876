import { Injectable } from '@angular/core';
import { SpinnerAction } from '../../redux/spinner/actions';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpResponse
} from '@angular/common/http';

import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/throw';
import 'rxjs/add/operator/catch';

import {Router} from '@angular/router';


@Injectable()
export class AuthHttpInterceptor implements HttpInterceptor {

  constructor(private actions: SpinnerAction, private router: Router) {
  }

  static counter = 0;

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Clone the request to add the new header.
    let authReq: HttpRequest<any> = req.clone();
    if (localStorage.getItem('access_token')) {
      authReq = req.clone({
        setHeaders: {
          Authorization: localStorage.getItem('access_token')
        }
      });
    }
    // send the newly created request
    return next.handle(authReq)
      .do(event => {
          if (event.type === 0) {
            if (!AuthHttpInterceptor.counter && !authReq.headers.has('disable-spinner')) {
              this.actions.showSpinner();
            }
            AuthHttpInterceptor.counter++;
          }
          if (event instanceof HttpResponse) {
            if (!--AuthHttpInterceptor.counter) {
              this.actions.hideSpinner();
            }
          }
          return event;
        },
        (error) => {
          AuthHttpInterceptor.counter--;
          if (!AuthHttpInterceptor.counter) {
            this.actions.hideSpinner();
          }
          if (error instanceof HttpErrorResponse) {
            const url = this.router.url;
            if (error.status === 401 && !url.includes('/psw/resetPassword/')) {
              // intercept the respons error and displace it to the console
              this.router.navigate(['/login']);
              // return the error to the method that called it
              return Observable.throw(error);
            }
          }
        });
  }
}
