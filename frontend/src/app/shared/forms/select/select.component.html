<div [formGroup]="formGroup">
    <select class="app-select" [formControlName]="name" [formControl]="formGroup.controls[name]">
      <option value=""></option>
      <option *ngFor="let item of options" [attr.value]="item" [selected]="item===formGroup.controls[name].value">
        {{item}}
      </option>
      <option *ngFor="let item of optionsMap" [attr.value]="item.value" [selected]="item.value===formGroup.controls[name].value">
        {{item.title}}
      </option>
    </select>
    <span class="text-danger"
          *ngIf="formGroup.controls[name].hasError('required') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">Campo Obbligatorio</span>
</div>
<div class="col-sm-4">
  <small *ngIf="required&&!disabled" class="text-muted">required</small>
</div>
