@import "variables";
@import 'colors';

.autolettura {
  background: url("/assets/img/optimaIcons/autolettura_color.png") no-repeat center;
  background-size: contain;
}

.modifica {
  background: url("/assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

.info-circle {
  border-radius: 50%;
  border: 1px solid;
  height: 26px;
  width: 26px;
  text-align: center;
  font-style: italic;
  line-height: 26px;
  font-family: serif;
  font-weight: bold;
  font-size: 24px;
  display: inline-block;
  color: #3f7ba2;
  cursor: pointer;
}

.app-select {
  border-radius: 7px;
  height: 34px;
  padding: 2px 25px;
  font-weight: 500;
  border-color: $menu-border;
  color: $dark-blue;
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.service-chart-block {
  padding: 0;
  margin-top: 1%;
  border: 1px solid $menu-border;
  border-radius: 5px;

  .row {
    margin: 1%;
    margin-top: -14%;
  }

  .labels {
    width: 90%;
    margin-left: -9%;
    margin-top: 1%;
  }
  .info-circle {
    float: left;
    margin-top: 1%;
  }
  .icon-chart-dot {
    font-size: 30px;
    color: #e1523d;
  }
  .select-block {
    margin-top: 1%;
    margin-bottom: 1%;
    select {
      float: left;
      width: 60%;
    }
  }
  .service-chart {
    margin-top: 1%;
    margin-bottom: 1%;
  }

  .legend {
    display: flex;
    position: absolute;
    margin-left: 60%;
    margin-top: 8%;
    flex-direction: column;
    .legend-color {
      width: 20px;
      height: 16px;
      display: flex;
    }
    div {
      margin-top: 1%
    }
    span {
      margin-left: 30px;
    }
  }
  .tuoi-consumi {
    height: 10px;
    background-color: $grey;
  }
  .consumi-del-in-corso {
    height: 10px;
    background-color: #00a1ee;
  }
  .f-23 {
    height: 2px;
    margin-bottom: 3px;
    background-color: #b8b8b8;
  }
}

@media only screen and (max-width: 768px) {
  .service-chart-block {
    border: none;
    .app-select {
      margin-bottom: 5%;
    }

    .select-block {
      select {
        width: 80%;
      }
    }
  }
}

.pagination-nav {
  cursor: pointer;
  color: #07aceb;
  font-size: 25px;
  font-weight: bold;
  position: absolute;
  margin: 60%;
  margin-top: 50%;
  margin-left: 70%;
}

.right {
  float: right;
}

.text-align-middle {
  text-align: center;
}

.no-result-message {
  color: #e54c36;
  font-size: 24px;
  text-align: center;
}

.no-padding {
  padding: 0;
}

.pointer {
  cursor: pointer;

}

.pointer {
  cursor: pointer;

}

.block-menu {
  margin-top: 2%;
  padding: 4%;
  border-radius: 10px;
  background: white;
  min-height: 50vh
}

.font-weight-bold {
  font-weight: bold;
}

.coral {
  color: $coral;
}

.night-blue {
  color: #36749d;
}


.aqua-blue {
  color: $aqua-blue;
}

.accept-button-default {
  text-align: center;
  border: 1px solid #36749d;
  color: #36749d;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

.callback-icon {
  background: url(/assets/img/icons/icons8-callback-96.svg) no-repeat right white;
  background-size: contain;
}

.checkbox-container {
  display: block;
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .mark {
    position: absolute;
    height: 20px;
    width: 20px;
    background-color: #ffffff;
    border: 1px solid #b6cce3;
  }

  .disabled {
    border: 2px solid rgb(189, 189, 189);
    opacity: 0.9;
    cursor: not-allowed;
  }

  border-radius: 5px;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.checkbox-container input[type=checkbox][disabled]{
  cursor: not-allowed;
}

.mark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.checkbox-container input:checked ~ .mark:after {
  display: block;
}

/* Style the checkmark/indicator */
.checkbox-container .mark:after {
  left: 7px;
  top: 0;
  width: 5px;
  height: 15px;
  border: solid #37729a;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.default-app-button {
  border: 1px solid $dark-blue;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 5px 20px;
}

.page-title {
  background: #ffffff;
  border: 2px solid $menu-border;
  border-radius: 5px;
  height: 45px;

  .title-text {
    color: $dark-blue;
    margin-top: 11px;
    float: left;
  }
}

.no-padding {
  padding: 0;
}

.bordered {
  border: 1px solid $menu-border;
  border-radius: 5px;
}

.app-input {
  border: 1px solid $menu-border;
  border-radius: 5px;
  height: 30px;
  background-color: #ffffff;
}

.app-button {
  border: 1px solid $dark-blue;
  padding: 5px 15px;
  border-radius: 5px;
  float: left;
  cursor: pointer;

  a {
    text-decoration: none;
    color: $dark-blue;
  }
}

@media screen and (max-width: 991px) {
  .no-padding-mobile {
    padding: 0;
  }
}
.d-none{
  display: none;
}
