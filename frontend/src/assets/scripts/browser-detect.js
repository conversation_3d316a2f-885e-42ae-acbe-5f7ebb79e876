
// angular 5 Browser support
// https://v5.angular.io/guide/browser-support
// $(document).ready(function () { });

if (document.addEventListener) {
    document.addEventListener("DOMContentLoaded", function () { init(); });
} else if (document.attachEvent) {
    document.attachEvent("onreadystatechange", function () { init(); });
}

function init() {
    var brwData = this.searchBrwData(this.dataBrowser) || null;
    var osData = searchOsData();
    var isSupport = this.checkSupport(brwData, osData);
    // var isLoaded = this.checkAngularIsLoaded();

    if (isSupport) {
        // this.consoleLogData(brwData, osData, isSupport);
    } else {
        alert(
            "Il tuo browser non è idoneo alla navigazione nell’Area Clienti." +
            "\n" +
            "Procedi all’aggiornamento oppure utilizzane un altro."
        );
    }
}

function checkAngularIsLoaded() {
    var isLoaded = false;
    var element = document.getElementsByTagName("app-root")[0];
    isLoaded = element.childNodes.length > 0;
    return isLoaded;
}

function consoleLogData(brwData, osData, isSupport) {
    console.log(
        "\n" +
        "os.Name = " + osData + "\n" +
        "brw.name = " + brwData.browserName + "\n" +
        "brw.fullVersion = " + brwData.fullVersion + "\n" +
        "brw.majorVersion = " + brwData.majorVersion + "\n" +
        "brw.minSupportVersion = " + brwData.minSupportVersion + "\n" +
        "isSupport = " + isSupport + "\n" +
        "navigator.appName = " + brwData.navigatorAppName + "\n" +
        "navigator.userAgent = " + brwData.navigatorUserAgent + "\n" +
        "\n"
    );
}



function checkSupport(brwData, osData) {
    var result = +brwData.majorVersion >= +brwData.minSupportVersion;
    //
    return result;
}

function searchOsData() {
    var osData = null;
    if (navigator.appVersion.indexOf("Win") != -1) osData = "Windows";
    if (navigator.appVersion.indexOf("Mac") != -1) osData = "MacOS";
    if (navigator.appVersion.indexOf("X11") != -1) osData = "UNIX";
    if (navigator.appVersion.indexOf("Linux") != -1) osData = "Linux";
    //
    return osData;
}

function searchBrwData(dataBrowser) {
    var nVer = navigator.appVersion;
    var nAgt = navigator.userAgent;
    var browserName = navigator.appName;
    var fullVersion = '' + parseFloat(navigator.appVersion);
    var majorVersion = parseInt(navigator.appVersion, 10);
    var minSupportVersion = 0;
    var nameOffset, verOffset, ix;


    // In Opera, the true version is after "Opera" or after "Version"
    if ((verOffset = nAgt.indexOf("OPR")) != -1) {
        browserName = "Opera";
        fullVersion = nAgt.substring(verOffset + 4);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            fullVersion = nAgt.substring(verOffset + 8);
        minSupportVersion = 55; // Opera latest. latest - 55 / https://en.wikipedia.org/wiki/History_of_the_Opera_web_browser
    }
    // In Opera, the true version is after "Opera" or after "Version"
    if ((verOffset = nAgt.indexOf("Opera")) != -1) {
        browserName = "Opera";
        fullVersion = nAgt.substring(verOffset + 6);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            fullVersion = nAgt.substring(verOffset + 8);
        minSupportVersion = 55; // Opera latest. latest - 55 / https://en.wikipedia.org/wiki/History_of_the_Opera_web_browser
    }
    // In MSIE, the true version is after "MSIE" in userAgent
    else if ((verOffset = nAgt.indexOf("MSIE")) != -1) {
        browserName = "Microsoft Internet Explorer";
        fullVersion = nAgt.substring(verOffset + 5);
        minSupportVersion = 9; // versions - 9, 10, 11 / https://en.wikipedia.org/wiki/Internet_Explorer_version_history
    }

    // In Trident, the true version is after "Trident" in userAgent
    else if ((verOffset = nAgt.indexOf("Trident")) != -1) {
        browserName = "Microsoft Internet Explorer";
        fullVersion = nAgt.substring(verOffset + 8);
        if ((verOffset = nAgt.indexOf("rv:")) != -1)
            fullVersion = nAgt.substring(verOffset + 3);
        minSupportVersion = 5; // versions - 5, 6, 7, 8 (ie - 9,10,11) / https://en.wikipedia.org/wiki/Trident_(software)
    }

    // In Edge, the true version is after "Edge" in userAgent
    else if ((verOffset = nAgt.indexOf("Edge")) != -1) {
        browserName = "Edge";
        fullVersion = nAgt.substring(verOffset + 5);
        minSupportVersion = 12; // (16,17) 2 most recent major versions. latest - 17 / https://en.wikipedia.org/wiki/Microsoft_Edge
    }

    // In Chrome, the true version is after "Chrome"
    else if ((verOffset = nAgt.indexOf("Chrome")) != -1) {
        browserName = "Chrome";
        fullVersion = nAgt.substring(verOffset + 7);
        minSupportVersion = 33; // Chrome latest. latest - 68 / https://en.wikipedia.org/wiki/Google_Chrome_version_history
    }
    // In Safari, the true version is after "Safari" or after "Version"
    else if ((verOffset = nAgt.indexOf("Safari")) != -1) {
        browserName = "Safari";
        fullVersion = nAgt.substring(verOffset + 7);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            fullVersion = nAgt.substring(verOffset + 8);
        minSupportVersion = 4; // (4, 5) 2 most recent major versions / https://en.wikipedia.org/wiki/Safari_version_history
    }
    // In Firefox, the true version is after "Firefox"
    else if ((verOffset = nAgt.indexOf("Firefox")) != -1) {
        browserName = "Firefox";
        fullVersion = nAgt.substring(verOffset + 8);
        minSupportVersion = 44; // Firefox latest. latest - 62 / https://en.wikipedia.org/wiki/Firefox_version_history
    }
    // In most other browsers, "name/version" is at the end of userAgent
    else if ((nameOffset = nAgt.lastIndexOf(' ') + 1) <
        (verOffset = nAgt.lastIndexOf('/'))) {
        browserName = nAgt.substring(nameOffset, verOffset);
        fullVersion = nAgt.substring(verOffset + 1);
        if (browserName.toLowerCase() == browserName.toUpperCase()) {
            browserName = navigator.appName;
        }
    }
    // trim the fullVersion string at semicolon/space if present
    if ((ix = fullVersion.indexOf(";")) != -1)
        fullVersion = fullVersion.substring(0, ix);
    if ((ix = fullVersion.indexOf(" ")) != -1)
        fullVersion = fullVersion.substring(0, ix);
    if ((ix = fullVersion.indexOf(")")) != -1)
        fullVersion = fullVersion.substring(0, ix);

    majorVersion = parseInt('' + fullVersion, 10);
    if (isNaN(majorVersion)) {
        fullVersion = '' + parseFloat(navigator.appVersion);
        majorVersion = parseInt(navigator.appVersion, 10);
    }

    var brwData = {
        browserName: browserName,
        fullVersion: fullVersion,
        majorVersion: majorVersion,
        navigatorAppName: navigator.appName,
        navigatorUserAgent: navigator.userAgent,
        minSupportVersion: minSupportVersion
    };
    //
    return brwData;
}


function searchBrwVersion(dataString) {
    var index = dataString.indexOf(this.versionSearchString);
    if (index === -1) {
        return;
    }

    var rv = dataString.indexOf("rv:");
    if (this.versionSearchString === "Trident" && rv !== -1) {
        return parseFloat(dataString.substring(rv + 3));
    } else {
        return parseFloat(dataString.substring(index + this.versionSearchString.length + 1));
    }
}

// // angular 5 Browser support
// // https://v5.angular.io/guide/browser-support
// var dataBrowser = [
//     {
//         string: navigator.userAgent, subString: "Edge", identity: "MS Edge",
//         minSupportedversion: '16' // 2 most recent major versions. latest - 17 / https://en.wikipedia.org/wiki/Microsoft_Edge
//     },
//     {
//         string: navigator.userAgent, subString: "MSIE", identity: "Explorer",
//         minSupportedversion: '9', // versions - 9, 10, 11 / https://en.wikipedia.org/wiki/Internet_Explorer_version_history
//     },
//     {
//         string: navigator.userAgent, subString: "Trident", identity: "Explorer",
//         minSupportedversion: '7' // ???
//     },
//     {
//         string: navigator.userAgent, subString: "Firefox", identity: "Firefox",
//         minSupportedversion: '62' // Firefox latest. latest - 62 / https://en.wikipedia.org/wiki/Firefox_version_history
//     },
//     {
//         string: navigator.userAgent, subString: "Opera", identity: "Opera",
//         minSupportedversion: '55' // Opera latest. latest - 55 / https://en.wikipedia.org/wiki/History_of_the_Opera_web_browser
//     },
//     {
//         string: navigator.userAgent, subString: "OPR", identity: "Opera",
//         minSupportedversion: '55' // Opera latest. latest - 55 / https://en.wikipedia.org/wiki/History_of_the_Opera_web_browser
//     },

//     {
//         string: navigator.userAgent, subString: "Chrome", identity: "Chrome",
//         minSupportedversion: '68' // Chrome	latest. latest - 68 / https://en.wikipedia.org/wiki/Google_Chrome_version_history
//     },
//     {
//         string: navigator.userAgent, subString: "Safari", identity: "Safari",
//         minSupportedversion: '5' // 2 most recent major versions / https://en.wikipedia.org/wiki/Safari_version_history
//     }
//     // brw mobile
//     // IE Mobile
//     // iOS
//     // Android
// ]


