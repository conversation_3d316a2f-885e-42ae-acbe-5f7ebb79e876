package com.optima.security.controller;


import com.optima.security.model.Login;
import com.optima.security.model.prospect.*;
import com.optima.security.service.ProspectUserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/prospect")
public class ProspectUserSecurityController {


    private final ProspectUserService prospectUserService;

    public ProspectUserSecurityController(ProspectUserService prospectUserService) {
        this.prospectUserService = prospectUserService;
    }

    @PostMapping("/register")
    public ResponseEntity<?> registerProspectUser(@RequestBody ProspectUserRegister registerBody) {
        return prospectUserService.addNewProspectUser(registerBody);
    }

    @PostMapping("/login")
    public ResponseEntity<?> loginProspectUser(@RequestBody Login credentials) {
        return prospectUserService.login(credentials);
    }

    @GetMapping("/isExpired")
    public ProspectUserEntity loginProspectUser(@RequestParam(value = "access_token", required = false) String token) {
        return prospectUserService.findUserByToken(token);
    }

    @PostMapping("/setPassword")
    public ForgotPasswordResponse setPassword(@RequestBody ForgotPasswordFormRequest requestBody, HttpServletRequest request) {
        return prospectUserService.resetPassword(requestBody, request.getHeader("Auth"));
    }
}
