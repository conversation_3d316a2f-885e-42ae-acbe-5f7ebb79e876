package com.optima.security.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class AuthenticationToken {

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("status_code")
    private Integer statusCode;

    private String scope;

    @JsonProperty("token_type")
    private String tokenType;

    @JsonProperty("expires_in")
    private Integer expiresIn;

    private Long expiration;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public Integer getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Integer expiresIn) {
        this.expiration = System.currentTimeMillis() + expiresIn*1000;
        this.expiresIn = expiresIn;
    }

    public Long getExpiration() {
        return expiration;
    }

    public void setExpiration(Long expiration) {
        this.expiration = expiration;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AuthenticationToken that = (AuthenticationToken) o;
        return Objects.equals(accessToken, that.accessToken) &&
                Objects.equals(statusCode, that.statusCode) &&
                Objects.equals(scope, that.scope) &&
                Objects.equals(tokenType, that.tokenType) &&
                Objects.equals(expiresIn, that.expiresIn) &&
                Objects.equals(expiration, that.expiration);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accessToken, statusCode, scope, tokenType, expiresIn, expiration);
    }

    @Override
    public String toString() {
        return "AuthenticationToken{" +
                "statusCode=" + statusCode +
                ", scope='" + scope + '\'' +
                ", tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", expiration=" + expiration +
                '}';
    }
}
