package com.optima.security.service;

import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.UserEntity;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.Authentication;

public interface JwtTokenService {

    String buildProlongedToken(Authentication authentication) throws JwtTokenException;

    String buildProlongedAdminToken(Authentication authentication, String UserId) throws JwtTokenException;

    String buildDisposableToken(Long userId) throws JwtTokenException;

    String buildResetPasswordToken(UserEntity user) throws JwtTokenException;

    String buildWso2Token(String clientId, String ipAddress) throws JwtTokenException;

    AbstractAuthenticationToken validateToken(String token);

}