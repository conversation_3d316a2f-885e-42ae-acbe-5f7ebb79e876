package com.optima.security.service;

import com.optima.security.model.Login;
import com.optima.security.model.prospect.*;
import org.springframework.http.ResponseEntity;

public interface ProspectUserService {

    ResponseEntity<?> addNewProspectUser(ProspectUserRegister prospectUserRegister);

    ResponseEntity<?> login(Login credential);

    ProspectUserEntity findUserByToken(String token);

    ForgotPasswordResponse resetPassword(ForgotPasswordFormRequest passwords, String token);
}
