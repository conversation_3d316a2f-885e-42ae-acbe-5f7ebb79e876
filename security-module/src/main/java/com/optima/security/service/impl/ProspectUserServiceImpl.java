package com.optima.security.service.impl;

import com.optima.security.model.Login;
import com.optima.security.model.prospect.*;
import com.optima.security.processors.TokenService;
import com.optima.security.repository.ProspectUserRepository;
import com.optima.security.service.ProspectUserService;
import com.optima.security.util.OptimaSHAPasswd;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ProspectUserServiceImpl implements ProspectUserService {

    private final OptimaSHAPasswd optimaSHAPasswd;
    private final ProspectUserRepository userRepository;
    @Qualifier("ResetPswTokenServiceImpl")
    TokenService tokenService;
    private static final Logger logger = LogManager.getLogger(ProspectUserServiceImpl.class);


    public ProspectUserServiceImpl(OptimaSHAPasswd optimaSHAPasswd, ProspectUserRepository userRepository, @Qualifier("ResetPswTokenServiceImpl") TokenService tokenService) {
        this.optimaSHAPasswd = optimaSHAPasswd;
        this.userRepository = userRepository;
        this.tokenService = tokenService;
    }

    @Override
    @Transactional
    public ResponseEntity<?> addNewProspectUser(ProspectUserRegister userRegister) {
        ProspectUserEntity user = new ProspectUserEntity(userRegister);
        if (userRegister.getPassword1().equals(userRegister.getPassword2())) {
            user.setPassword(optimaSHAPasswd.code(userRegister.getPassword1()));
            if (userRepository.findByCF(user.getCF()) == null) {
                userRepository.save(user);
                logger.info("A new user saved successfully");
                return new ResponseEntity<>(HttpStatus.OK);
            } else {
                logger.error("Error - a new user didn't save. User with CF = {} already exists", user.getCF());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        } else {
            logger.error("Passwords do not match");
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public ResponseEntity<?> login(Login credential) {
        ProspectUserEntity prospectUser = userRepository.findByCF(credential.getUsername());
        if (prospectUser != null && optimaSHAPasswd.compare(prospectUser.getPassword(), credential.getPassword())) {
            logger.info("Prospect user authenticated. Client Codice Fiscale o Partita IVA: {}", credential.getUsername());
            userRepository.updateLastAccessDate(prospectUser.getId());
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            logger.error("Authentication for prospect user with CF/PIVA {} failed.", credential.getUsername());
            return new ResponseEntity<>("INVALID_CREDENTIALS", HttpStatus.FORBIDDEN);
        }
    }

    @Override
    public ProspectUserEntity findUserByToken(String token) {
        logger.info("Finding user by token");
        return userRepository.findByToken(token);
    }

    @Override
    public ForgotPasswordResponse resetPassword(ForgotPasswordFormRequest passwords, String token) {
        ProspectUserEntity user = userRepository.findByToken(token);
        if (user != null && passwords.getPasswords().getPassword1().equals(passwords.getPasswords().getPassword2())) {
            logger.info("Updating password for user with id - {}", user.getId());
            String hashedPassword = optimaSHAPasswd.code(passwords.getPasswords().getPassword1());
            userRepository.updateUserPassword(user.getId(), hashedPassword, token);
            userRepository.updateUserToken(user.getId());
            logger.info("Password successfully updated for user with id - {}", user.getId());
            return new ForgotPasswordResponse(200, "Password successfully updated");
        } else {
            logger.error("User not found");
            return new ForgotPasswordResponse(400, "User not found");
        }
    }
}
